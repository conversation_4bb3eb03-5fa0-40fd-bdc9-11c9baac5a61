// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-1.0.x", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(Explorer)
  bio           String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]
  trips         Trip[]
  purchases     TripPurchase[]
  batchJobs     BatchJob[]

  @@map("users")
}

enum UserRole {
  Explorer  // Utenti che fruiscono dei viaggi
  Ranger    // Utenti che possono creare e fruire dei viaggi
  Sentinel  // Ranger con poteri amministrativi
}

model EmailVerificationToken {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("email_verification_tokens")
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("password_reset_tokens")
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
  @@map("verificationtokens")
}

model Trip {
  id                 String            @id @default(cuid())
  title              String
  summary            String
  destination        String
  duration_days      Int
  duration_nights    Int
  tags               String[]
  theme              String
  characteristics    String[]
  recommended_seasons RecommendedSeason[]
  media              Json[]            @default([])
  gpxFile            Json?             // Aggiunto campo per il file GPX
  insights           String?           @db.Text
  slug               String            @unique
  status             TripStatus        @default(Bozza)
  price              Decimal           @default(5.00) @db.Decimal(10,2)
  travelDate         DateTime?         // Data in cui il ranger ha fatto il viaggio
  created_at         DateTime          @default(now())
  updated_at         DateTime          @updatedAt
  user_id            String
  user               User              @relation(fields: [user_id], references: [id])
  stages             Stage[]           // Relazione con le tappe
  purchases          TripPurchase[]

  @@map("trips")
}

model Stage {
  id                String     @id @default(cuid())
  tripId            String
  orderIndex        Int        // Numero ordinale della tappa (1, 2, 3...)
  title             String
  description       String?    @db.Text
  routeType         String?    // Tipo di percorso (testo libero)
  duration          String?    // Durata stimata (testo libero)
  media             Json[]     @default([])
  gpxFile           Json?      // Traccia GPX della singola tappa
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt
  trip              Trip       @relation(fields: [tripId], references: [id], onDelete: Cascade)
  
  @@unique([tripId, orderIndex])
  @@map("stages")
}

enum RecommendedSeason {
  Primavera
  Estate
  Autunno
  Inverno
}

enum TripStatus {
  Bozza
  Pronto_per_revisione @map("Pronto per revisione")
  Pubblicato
  Archiviato
}

model TripPurchase {
  id              String          @id @default(cuid())
  userId          String
  tripId          String
  amount          Decimal         @db.Decimal(10,2)
  status          PurchaseStatus  @default(PENDING)
  paymentMethod   String?         // "mock", "stripe", etc.
  stripePaymentId String?         // ID Stripe per future implementazioni
  purchasedAt     DateTime?       // Quando l'acquisto è stato completato
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  trip            Trip            @relation(fields: [tripId], references: [id], onDelete: Cascade)
  transactions    TripPurchaseTransaction[]

  @@unique([userId, tripId])
  @@map("trip_purchases")
}

model TripPurchaseTransaction {
  id                String            @id @default(cuid())
  purchaseId        String
  amount            Decimal           @db.Decimal(10,2)
  status            PurchaseStatus
  paymentMethod     String?           // "mock", "stripe", etc.
  stripePaymentId   String?           // ID Stripe per questo tentativo
  failureReason     String?           // Motivo del fallimento
  metadata          Json?             // Dati aggiuntivi per debugging
  createdAt         DateTime          @default(now())
  
  purchase          TripPurchase      @relation(fields: [purchaseId], references: [id], onDelete: Cascade)

  @@map("trip_purchase_transactions")
}

enum PurchaseStatus {
  PENDING   // Acquisto iniziato ma non completato
  COMPLETED // Acquisto completato con successo
  FAILED    // Acquisto fallito
  REFUNDED  // Acquisto rimborsato
}

model BatchJob {
  id              String          @id @default(cuid())
  userId          String
  status          BatchJobStatus  @default(PENDING)
  zipFileUrl      String          // URL to the uploaded ZIP in blob storage
  totalTrips      Int
  processedTrips  Int             @default(0)
  createdTripIds  String[]
  errors          Json?           // Store errors as a JSON object
  startedAt       DateTime        @default(now())
  completedAt     DateTime?
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("batch_jobs")
}

enum BatchJobStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}
