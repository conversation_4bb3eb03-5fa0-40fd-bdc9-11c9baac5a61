// src/lib/batch/BatchProcessor.ts
import { ZipParser, ParsedTrip, ParsedMediaFile, ParsedGpxFile } from './ZipParser'
import { BatchProcessingResult } from '@/schemas/batch-trip'
import { prisma } from '@/lib/core/prisma'
import { getStorageProvider } from '@/lib/storage'
import { MediaItem, GpxFile } from '@/types/trip'
import { RecommendedSeason, BatchJobStatus, Prisma } from '@prisma/client'
import { put } from '@vercel/blob'

interface BatchError {
  message: string;
  tripIndex?: number;
}

export class BatchProcessor {
  private storageProvider = getStorageProvider()

  async startBatchJob(userId: string, zipBuffer: Buffer): Promise<string> {
    // 1. Upload ZIP to blob storage first
    const zipFileName = `batch-uploads/batch_${Date.now()}.zip`
    const { url: zipFileUrl } = await put(zipFileName, zipBuffer, {
      access: 'public',
      contentType: 'application/zip',
    });

    // 2. Create job record in database
    const job = await prisma.batchJob.create({
      data: {
        userId,
        zipFileUrl,
        status: BatchJobStatus.PENDING,
        totalTrips: 0, // Will be updated after parsing
        createdTripIds: [],
      },
    });

    // 3. Start processing asynchronously (fire and forget)
    this.processJobAsync(job.id).catch((error) => {
      console.error(`Fatal error in processJobAsync for job ${job.id}:`, error)
      // Log critical failure to the job itself
      prisma.batchJob.update({
        where: { id: job.id },
        data: {
          status: BatchJobStatus.FAILED,
          errors: {
            push: {
              message: 'Processamento fallito in modo critico.' 
            }
          },
          completedAt: new Date(),
        },
      }).catch(e => console.error(`Failed to update job status on critical failure for job ${job.id}:`, e));
    })

    return job.id
  }

  async getJobStatus(jobId: string): Promise<BatchProcessingResult | null> {
    const job = await prisma.batchJob.findUnique({
      where: { id: jobId },
    })

    if (!job) return null

    // Adapt the Prisma model to the BatchProcessingResult schema
    return {
      jobId: job.id,
      status: job.status.toLowerCase() as 'pending' | 'processing' | 'completed' | 'failed',
      totalTrips: job.totalTrips,
      processedTrips: job.processedTrips,
      createdTripIds: job.createdTripIds,
      errors: Array.isArray(job.errors)
        ? (job.errors as unknown as BatchError[])
        : (job.errors ? [job.errors as unknown as BatchError] : []),
      startedAt: job.startedAt,
      completedAt: job.completedAt ?? undefined,
    }
  }

  private async processJobAsync(jobId: string): Promise<void> {
    // 1. Set job to PROCESSING
    await prisma.batchJob.update({
      where: { id: jobId },
      data: { status: BatchJobStatus.PROCESSING },
    })

    const job = await prisma.batchJob.findUnique({ where: { id: jobId } })
    if (!job) throw new Error('Job non trovato dopo averlo impostato come in elaborazione')

    try {
      // 2. Download ZIP from storage
      const response = await fetch(job.zipFileUrl)
      if (!response.ok) {
        throw new Error(`Impossibile scaricare il file ZIP: ${response.statusText}`)
      }
      const zipBuffer = Buffer.from(await response.arrayBuffer())

      // 3. Parse ZIP and update total trips
      const parser = new ZipParser()
      await parser.loadZip(zipBuffer)
      const validationErrors = parser.validateZipStructure()
      if (validationErrors.length > 0) {
        throw new Error(`Struttura ZIP non valida: ${validationErrors.join(', ')}`)
      }
      const parsedData = await parser.parse()

      await prisma.batchJob.update({
        where: { id: jobId },
        data: { totalTrips: parsedData.trips.length },
      })

      // 4. Process each trip
      for (let i = 0; i < parsedData.trips.length; i++) {
        const tripData = parsedData.trips[i]
        try {
          const createdTripId = await this.processSingleTrip(job.userId, tripData, i)
          
          await prisma.batchJob.update({
            where: { id: jobId },
            data: {
              processedTrips: { increment: 1 },
              createdTripIds: { push: createdTripId },
            },
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Errore sconosciuto'
          await prisma.batchJob.update({
            where: { id: jobId },
            data: {
              errors: {
                push: { tripIndex: i, message: errorMessage }
              },
            },
          })
        }
      }

      // 5. Finalize job status
      const finalJobState = await prisma.batchJob.findUnique({ where: { id: jobId } })
      const finalStatus = (finalJobState?.createdTripIds.length ?? 0) > 0 ? BatchJobStatus.COMPLETED : BatchJobStatus.FAILED

      await prisma.batchJob.update({
        where: { id: jobId },
        data: {
          status: finalStatus,
          completedAt: new Date(),
        },
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Errore generale di processamento'
      await prisma.batchJob.update({
        where: { id: jobId },
        data: {
          status: BatchJobStatus.FAILED,
          errors: {
            push: { message: errorMessage }
          },
          completedAt: new Date(),
        },
      })
    }
  }

  private async processSingleTrip(userId: string, tripData: ParsedTrip, tripIndex: number): Promise<string> {
    // This method remains largely the same, but it's now just focused on the logic
    // of creating a single trip from parsed data.
    const tripMedia = await this.processMediaFiles(tripData.media, `trip-${tripIndex}`)
    const tripGpxFile = tripData.gpxFile ? await this.processGpxFile(tripData.gpxFile, `trip-${tripIndex}`) : null

    const processedStages: Array<{
      stageData: ParsedTrip['stages'][0];
      media: MediaItem[];
      gpxFile: GpxFile | null;
    }> = [];

    for (let stageIndex = 0; stageIndex < tripData.stages.length; stageIndex++) {
      const stageData = tripData.stages[stageIndex]
      const stageMedia = await this.processMediaFiles(stageData.media, `trip-${tripIndex}-stage-${stageIndex}`)
      const stageGpxFile = stageData.gpxFile ? await this.processGpxFile(stageData.gpxFile, `trip-${tripIndex}-stage-${stageIndex}`) : null
      processedStages.push({ stageData, media: stageMedia, gpxFile: stageGpxFile })
    }

    return await prisma.$transaction(async (tx) => {
      const slug = this.slugify(tripData.title)
      const newTrip = await tx.trip.create({
        data: {
          title: tripData.title,
          summary: tripData.summary,
          destination: tripData.destination,
          theme: tripData.theme,
          characteristics: tripData.characteristics,
          recommended_seasons: tripData.recommended_seasons as RecommendedSeason[],
          tags: tripData.tags,
          travelDate: tripData.travelDate,
          duration_days: Math.max(1, tripData.stages.length),
          duration_nights: 0,
          insights: null,
          media: tripMedia as unknown as Prisma.InputJsonValue[],
          gpxFile: tripGpxFile as unknown as Prisma.JsonObject,
          slug,
          user_id: userId,
        },
      })

      for (const { stageData, media, gpxFile } of processedStages) {
        await tx.stage.create({
          data: {
            tripId: newTrip.id,
            orderIndex: stageData.orderIndex,
            title: stageData.title,
            description: stageData.description || null,
            routeType: stageData.routeType || null,
            duration: stageData.duration || null,
            media: media as unknown as Prisma.InputJsonValue[],
            gpxFile: gpxFile as unknown as Prisma.JsonObject,
          },
        })
      }
      return newTrip.id
    })
  }

  private async processMediaFiles(mediaFiles: ParsedMediaFile[], prefix: string): Promise<MediaItem[]> {
    const mediaItems: MediaItem[] = []
    for (let i = 0; i < mediaFiles.length; i++) {
      const file = mediaFiles[i]
      try {
        const fileObj = new File([file.buffer], file.filename, { type: file.mimeType })
        const uploadResult = await this.storageProvider.uploadFile(fileObj, `${prefix}-${i}-${file.filename}`)
        mediaItems.push({
          id: `media_${Date.now()}_${i}`,
          type: file.mimeType.startsWith('image/') ? 'image' : 'video',
          url: uploadResult.url,
          caption: file.caption,
        })
      } catch (error) {
        console.warn(`Skipping media file ${file.filename} due to upload error`, error)
        continue
      }
    }
    return mediaItems
  }

  private async processGpxFile(gpxFile: ParsedGpxFile, prefix: string): Promise<GpxFile> {
    try {
      const fileObj = new File([gpxFile.buffer], gpxFile.filename, { type: 'application/gpx+xml' })
      const uploadResult = await this.storageProvider.uploadFile(fileObj, `${prefix}-${gpxFile.filename}`)
      return {
        url: uploadResult.url,
        filename: gpxFile.filename,
        waypoints: 0,
        distance: 0,
        isValid: true,
      }
    } catch (error) {
      console.error(`Error uploading GPX file ${gpxFile.filename}:`, error)
      throw new Error(`Errore caricamento GPX: ${gpxFile.filename}`)
    }
  }

  private slugify(text: string): string {
    return text.toString().toLowerCase().trim()
      .normalize('NFD').replace(/[\u0300-\u036f]/g, '')
      .replace(/\s+/g, '-').replace(/[^\w-]+/g, '-')
      .replace(/-+/g, '-').replace(/^-+/, '').replace(/-+$/, '')
  }

  static async cleanupOldJobs(maxAgeHours: number = 24): Promise<void> {
    const cutoff = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000)
    await prisma.batchJob.deleteMany({
      where: {
        startedAt: {
          lt: cutoff,
        },
      },
    })
  }
}
