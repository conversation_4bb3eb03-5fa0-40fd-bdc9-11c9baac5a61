{"functions": {"src/app/api/trips/batch/route.ts": {"maxDuration": 60}, "src/app/api/trips/batch/status/[jobId]/route.ts": {"maxDuration": 30}}, "rewrites": [{"source": "/dashboard/trips", "destination": "/dashboard/trips"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}